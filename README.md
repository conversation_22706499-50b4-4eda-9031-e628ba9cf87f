# 智慧图片生成 MCP 工具 - 智能模型选择版

基于魔搭平台API，为网页开发提供智能图片生成服务的MCP（Model Context Protocol）工具。

## ✨ 核心特性

- 🤖 **智能模型选择**: 根据用户需求自动选择最适合的AI模型
- 🎨 **多模型支持**: 内置3个精选专业模型
- 🧠 **内容分析**: 智能分析网页内容并生成适配图片
- 📏 **尺寸推断**: 根据用途自动推断最适合的图片尺寸
- 🔧 **零配置**: 只需3个环境变量即可使用
- 📊 **评分系统**: 基于多维度评估自动选择最优模型

## 🤖 内置模型

### 1. 苏-FLUX小红书极致真实V2
- **适用**: 真实风格、人物肖像、生活场景、小红书内容
- **特点**: 极致真实感、社交媒体风格

### 2. 麦橘超然 MajicFlus
- **适用**: 高质量人像、亚洲女性、角色设计
- **特点**: 专注人像质量、细节丰富

### 3. FLUX.1-Kontext-dev-lora-ArtAug
- **适用**: 艺术创作、美学增强、创意设计
- **特点**: 自动美学增强、色彩优化

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 构建项目
```bash
npm run build
```

### 3. 配置MCP
```json
{
  "mcpServers": {
    "zhihui-image": {
      "command": "node",
      "args": ["path/to/build/index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your-api-key",
        "OUTPUT_DIR": "path/to/output",
        "MAX_IMAGE_SIZE": "2048"
      }
    }
  }
}
```

## 🛠 可用工具

### 1. generate_image
基础图片生成功能

### 2. analyze_and_generate（推荐）
智能分析网页内容并自动生成适配图片

### 3. smart_size_inference
智能尺寸推断

### 4. list_available_models
查看所有可用模型

## 🧠 智能选择机制

系统会根据以下因素自动选择最适合的模型：

1. **图片类型匹配** - 根据imageType参数
2. **风格要求适配** - 分析style参数
3. **提示词内容分析** - 智能分析提示词内容
4. **质量要求评估** - 根据quality参数

## 🔧 环境变量

只需要3个环境变量：

- `MODELSCOPE_API_KEY`: 魔搭平台API密钥（必需）
- `OUTPUT_DIR`: 图片输出目录
- `MAX_IMAGE_SIZE`: 最大图片尺寸限制

## 📖 详细文档

查看 `INTELLIGENT_MODEL_GUIDE.md` 获取完整使用指南。

## 许可证

MIT License
