#!/usr/bin/env node

/**
 * 智慧图片生成 MCP 服务器
 * 基于魔搭平台API，为网页开发提供智能图片生成服务
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import { z } from "zod";
import axios from "axios";
import fs from "fs-extra";
import path from "path";
import crypto from "crypto";

// 配置接口
interface ModelScopeConfig {
  apiKey: string;
  outputDir: string;
  maxImageSize: number;
}

// 模型信息接口
interface ModelInfo {
  id: string;
  name: string;
  description: string;
  strengths: string[];
  suitableFor: string[];
  apiEndpoint: string;
  modelId: string;
  defaultParams: {
    num_inference_steps?: number;
    guidance_scale?: number;
    width?: number;
    height?: number;
  };
  specialInstructions?: string;
}

// 图片生成请求接口
interface GenerationRequest {
  prompt: string;
  width: number;
  height: number;
  imageType: string;
  style?: string;
  quality?: 'standard' | 'high' | 'ultra';
  context?: string;
}

// 图片生成结果接口
interface ImageGenerationResult {
  success: boolean;
  image?: {
    url: string;
    localPath: string;
    width: number;
    height: number;
    format: string;
    type: string;
    prompt: string;
    usageSuggestion: string;
  };
  error?: string;
}

// 内置模型配置
const AVAILABLE_MODELS: ModelInfo[] = [
  {
    id: 'flux-xiaohongshu-v2',
    name: '苏-FLUX小红书极致真实V2',
    description: '专注于生成极致真实的图片，特别适合小红书风格的内容',
    strengths: ['真实感', '小红书风格', '人物肖像', '生活场景'],
    suitableFor: ['portrait', 'lifestyle', 'realistic', 'social-media'],
    apiEndpoint: 'https://api-inference.modelscope.cn/v1/images/generations',
    modelId: 'yiwanji/FLUX_xiao_hong_shu_ji_zhi_zhen_shi_V2',
    defaultParams: {
      num_inference_steps: 20,
      guidance_scale: 7.5,
      width: 1024,
      height: 1024
    }
  },
  {
    id: 'majicflus-v1',
    name: '麦橘超然 MajicFlus',
    description: '专注高质量人像生成，擅长亚洲女性肖像',
    strengths: ['人像生成', '亚洲女性', '高质量', '细节丰富'],
    suitableFor: ['portrait', 'avatar', 'character', 'asian-female'],
    apiEndpoint: 'https://api-inference.modelscope.cn/v1/images/generations',
    modelId: 'MAILAND/majicflus_v1',
    defaultParams: {
      num_inference_steps: 25,
      guidance_scale: 8.0,
      width: 768,
      height: 1024
    }
  },
  {
    id: 'flux-artaug',
    name: 'FLUX.1-Kontext-dev-lora-ArtAug',
    description: '美学增强模型，提升图片的艺术质量和美感',
    strengths: ['美学增强', '艺术质量', '色彩优化', '构图改善'],
    suitableFor: ['artistic', 'aesthetic', 'creative', 'enhanced'],
    apiEndpoint: 'https://api-inference.modelscope.cn/v1/images/generations',
    modelId: 'DiffSynth-Studio/FLUX.1-Kontext-dev-lora-ArtAug',
    defaultParams: {
      num_inference_steps: 30,
      guidance_scale: 7.0,
      width: 1024,
      height: 1024
    },
    specialInstructions: 'Enhance the aesthetic quality of this image.'
  }
];

// 尺寸预设
const SIZE_PRESETS = {
  'logo': { width: 200, height: 80 },
  'icon': { width: 64, height: 64 },
  'button-icon': { width: 32, height: 32 },
  'hero-background': { width: 1920, height: 1080 },
  'section-background': { width: 1200, height: 600 },
  'card-image': { width: 400, height: 300 },
  'avatar': { width: 128, height: 128 },
  'thumbnail': { width: 300, height: 200 },
  'banner': { width: 800, height: 200 },
  'square': { width: 512, height: 512 }
};

// 获取配置
function getConfig(): ModelScopeConfig {
  return {
    apiKey: process.env.MODELSCOPE_API_KEY || '',
    outputDir: process.env.OUTPUT_DIR || './generated_images',
    maxImageSize: parseInt(process.env.MAX_IMAGE_SIZE || '2048')
  };
}

// 智能模型选择逻辑
function selectBestModel(request: GenerationRequest): ModelInfo {
  const { imageType, style, quality, context, prompt } = request;

  // 分析提示词和上下文
  const lowerPrompt = prompt.toLowerCase();
  const lowerContext = (context || '').toLowerCase();
  const combinedText = `${lowerPrompt} ${lowerContext}`;

  // 评分系统：为每个模型计算适合度分数
  const modelScores = AVAILABLE_MODELS.map(model => {
    let score = 0;

    // 基于图片类型评分
    if (model.suitableFor.includes(imageType)) {
      score += 30;
    }

    // 基于风格要求评分
    if (style) {
      if (model.suitableFor.includes(style.toLowerCase())) {
        score += 25;
      }
    }

    // 基于质量要求评分
    if (quality === 'ultra' && model.strengths.includes('高质量')) {
      score += 20;
    }

    // 基于提示词内容评分
    if (combinedText.includes('人像') || combinedText.includes('portrait') || combinedText.includes('人物')) {
      if (model.id === 'majicflus-v1' || model.id === 'flux-xiaohongshu-v2') {
        score += 25;
      }
    }

    if (combinedText.includes('真实') || combinedText.includes('realistic') || combinedText.includes('照片')) {
      if (model.id === 'flux-xiaohongshu-v2') {
        score += 30;
      }
    }

    if (combinedText.includes('艺术') || combinedText.includes('artistic') || combinedText.includes('美学')) {
      if (model.id === 'flux-artaug') {
        score += 30;
      }
    }

    if (combinedText.includes('女性') || combinedText.includes('女孩') || combinedText.includes('female')) {
      if (model.id === 'majicflus-v1') {
        score += 25;
      }
    }

    // 小红书相关关键词
    if (combinedText.includes('小红书') || combinedText.includes('生活') || combinedText.includes('lifestyle')) {
      if (model.id === 'flux-xiaohongshu-v2') {
        score += 35;
      }
    }

    return { model, score };
  });

  // 排序并选择最高分的模型
  modelScores.sort((a, b) => b.score - a.score);

  const selectedModel = modelScores[0];

  console.error(`🤖 智能模型选择结果:`);
  console.error(`  选中模型: ${selectedModel.model.name} (${selectedModel.model.id})`);
  console.error(`  适合度分数: ${selectedModel.score}`);
  console.error(`  选择原因: ${selectedModel.model.description}`);

  // 如果最高分太低，使用第一个模型作为默认
  if (selectedModel.score < 10) {
    console.error(`  分数过低，使用默认模型: ${AVAILABLE_MODELS[0].name}`);
    return AVAILABLE_MODELS[0];
  }

  return selectedModel.model;
}

// 确保输出目录存在
async function ensureOutputDir(outputDir: string): Promise<void> {
  await fs.ensureDir(outputDir);
}

// 生成唯一文件名
function generateFileName(type: string, format: string = 'png'): string {
  const timestamp = Date.now();
  const random = crypto.randomBytes(4).toString('hex');
  return `${type}_${timestamp}_${random}.${format}`;
}

// 调用智能模型API生成图片
async function callIntelligentModelAPI(
  request: GenerationRequest,
  config: ModelScopeConfig
): Promise<string> {
  try {
    // 智能选择最适合的模型
    const selectedModel = selectBestModel(request);

    // 构建最终提示词
    let finalPrompt = request.prompt;
    if (selectedModel.specialInstructions) {
      finalPrompt = `${selectedModel.specialInstructions} ${finalPrompt}`;
    }

    console.error(`🔗 智能API调用信息:`);
    console.error(`  选中模型: ${selectedModel.name}`);
    console.error(`  API端点: ${selectedModel.apiEndpoint}`);
    console.error(`  最终提示词: ${finalPrompt}`);
    console.error(`  尺寸: ${request.width}x${request.height}`);

    // 构建请求体（根据官方API格式）
    const requestBody = {
      model: selectedModel.modelId,
      prompt: finalPrompt
    };

    console.error(`📤 请求URL: ${selectedModel.apiEndpoint}`);
    console.error(`📤 请求体:`, JSON.stringify(requestBody, null, 2));

    const response = await axios.post(selectedModel.apiEndpoint, requestBody, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });

    console.error(`✅ API响应状态: ${response.status}`);
    console.error(`📄 响应数据:`, JSON.stringify(response.data, null, 2));

    // 处理魔搭平台的响应格式（根据官方API文档）
    if (response.data && response.data.images && response.data.images[0]) {
      const imageUrl = response.data.images[0].url;
      if (imageUrl) {
        console.error(`✅ 获取到图片URL: ${imageUrl}`);
        return imageUrl;
      }
    }

    throw new Error('API响应格式不正确或未找到图片数据');
  } catch (error) {
    console.error('魔搭平台API调用失败:', error);
    throw new Error(`图片生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 下载并保存图片
async function downloadAndSaveImage(
  imageUrl: string, 
  fileName: string, 
  outputDir: string
): Promise<string> {
  try {
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      timeout: 30000
    });
    
    const filePath = path.join(outputDir, fileName);
    await fs.writeFile(filePath, response.data);
    
    return filePath;
  } catch (error) {
    console.error('图片下载失败:', error);
    throw new Error(`图片下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 创建服务器实例
const server = new Server(
  {
    name: "zhihui-mcp",
    version: "1.0.0",
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 智能分析网页内容并生成提示词
function analyzeContentAndGeneratePrompt(
  content: string,
  imageType: string,
  context?: string
): { prompt: string; suggestedSize: { width: number; height: number } } {
  // 简化的内容分析逻辑
  const lowerContent = content.toLowerCase();
  let style = "modern, clean";
  let subject = "";

  // 分析内容类型和风格
  if (lowerContent.includes("business") || lowerContent.includes("corporate")) {
    style = "professional, corporate, clean";
  } else if (lowerContent.includes("creative") || lowerContent.includes("art")) {
    style = "creative, artistic, colorful";
  } else if (lowerContent.includes("tech") || lowerContent.includes("technology")) {
    style = "modern, tech, futuristic";
  }

  // 根据图片类型生成主题
  switch (imageType) {
    case "logo":
      subject = "logo design";
      break;
    case "icon":
      subject = "icon, symbol";
      break;
    case "background":
      subject = "background, abstract pattern";
      break;
    case "hero":
      subject = "hero image, banner";
      break;
    default:
      subject = "illustration";
  }

  // 构建提示词
  let prompt = `${subject}, ${style}`;
  if (context) {
    prompt += `, ${context}`;
  }
  prompt += ", high quality, professional";

  // 获取建议尺寸
  const suggestedSize = SIZE_PRESETS[imageType as keyof typeof SIZE_PRESETS] || SIZE_PRESETS.square;

  return { prompt, suggestedSize };
}

// 工具定义
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "generate_image",
        description: "基础图片生成功能，根据提示词生成指定尺寸的图片",
        inputSchema: {
          type: "object",
          properties: {
            prompt: {
              type: "string",
              description: "图片生成的提示词描述"
            },
            width: {
              type: "number",
              description: "图片宽度（像素）",
              default: 512
            },
            height: {
              type: "number",
              description: "图片高度（像素）",
              default: 512
            },
            imageType: {
              type: "string",
              description: "图片类型标识",
              enum: ["logo", "icon", "background", "hero", "content", "other"],
              default: "other"
            }
          },
          required: ["prompt"]
        }
      },
      {
        name: "analyze_and_generate",
        description: "智能分析网页内容并自动生成适配的图片（核心功能）",
        inputSchema: {
          type: "object",
          properties: {
            webpageContent: {
              type: "string",
              description: "网页内容或描述"
            },
            imageType: {
              type: "string",
              description: "需要生成的图片类型",
              enum: ["logo", "icon", "background", "hero", "card", "avatar", "banner"],
              default: "content"
            },
            context: {
              type: "string",
              description: "额外的上下文信息或特殊要求"
            },
            customSize: {
              type: "object",
              description: "自定义尺寸（可选）",
              properties: {
                width: { type: "number" },
                height: { type: "number" }
              }
            }
          },
          required: ["webpageContent", "imageType"]
        }
      },
      {
        name: "smart_size_inference",
        description: "根据图片用途智能推断最适合的尺寸",
        inputSchema: {
          type: "object",
          properties: {
            imageType: {
              type: "string",
              description: "图片类型",
              enum: Object.keys(SIZE_PRESETS)
            },
            usageContext: {
              type: "string",
              description: "使用场景描述"
            }
          },
          required: ["imageType"]
        }
      },
      {
        name: "list_available_models",
        description: "列出所有可用的AI模型及其特点，帮助了解智能模型选择机制",
        inputSchema: {
          type: "object",
          properties: {},
          required: []
        }
      }
    ]
  };
});

// 工具调用处理
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  const config = getConfig();

  // 确保args存在
  if (!args) {
    return {
      content: [
        {
          type: "text",
          text: "错误：缺少必需的参数"
        }
      ]
    };
  }

  // 检查API密钥
  if (!config.apiKey) {
    return {
      content: [
        {
          type: "text",
          text: "错误：未设置MODELSCOPE_API_KEY环境变量。请在MCP配置中设置您的魔搭平台API密钥。"
        }
      ]
    };
  }

  try {
    await ensureOutputDir(config.outputDir);

    switch (name) {
      case "generate_image": {
        const prompt = args.prompt as string;
        const width = (args.width as number) || 512;
        const height = (args.height as number) || 512;
        const imageType = (args.imageType as string) || "other";

        // 验证尺寸限制
        if (width > config.maxImageSize || height > config.maxImageSize) {
          return {
            content: [
              {
                type: "text",
                text: `错误：图片尺寸超出限制。最大尺寸：${config.maxImageSize}x${config.maxImageSize}`
              }
            ]
          };
        }

        console.error(`生成图片: ${prompt} (${width}x${height})`);

        // 构建生成请求
        const request: GenerationRequest = {
          prompt: prompt,
          width: width,
          height: height,
          imageType: imageType,
          quality: 'standard'
        };

        // 调用智能模型API生成图片
        const imageUrl = await callIntelligentModelAPI(request, config);

        // 下载并保存图片
        const fileName = generateFileName(imageType);
        const localPath = await downloadAndSaveImage(imageUrl, fileName, config.outputDir);
        const relativePath = `./${path.relative(process.cwd(), localPath)}`;

        const result: ImageGenerationResult = {
          success: true,
          image: {
            url: relativePath,
            localPath: localPath,
            width: width,
            height: height,
            format: "png",
            type: imageType,
            prompt: prompt,
            usageSuggestion: `适合用作${imageType}，尺寸${width}x${height}`
          }
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      case "analyze_and_generate": {
        const webpageContent = args.webpageContent as string;
        const imageType = args.imageType as string;
        const context = args.context as string;
        const customSize = args.customSize as { width?: number; height?: number };

        console.error(`智能分析生成: ${imageType} for content: ${webpageContent.substring(0, 100)}...`);

        // 分析内容并生成提示词
        const analysis = analyzeContentAndGeneratePrompt(webpageContent, imageType, context);

        // 确定最终尺寸
        const finalSize = customSize && customSize.width && customSize.height
          ? { width: customSize.width, height: customSize.height }
          : analysis.suggestedSize;

        // 验证尺寸限制
        if (finalSize.width > config.maxImageSize || finalSize.height > config.maxImageSize) {
          return {
            content: [
              {
                type: "text",
                text: `错误：推断的图片尺寸超出限制。最大尺寸：${config.maxImageSize}x${config.maxImageSize}`
              }
            ]
          };
        }

        // 构建智能生成请求
        const request: GenerationRequest = {
          prompt: analysis.prompt,
          width: finalSize.width,
          height: finalSize.height,
          imageType: imageType,
          context: context,
          quality: 'high'
        };

        // 生成图片
        const imageUrl = await callIntelligentModelAPI(request, config);

        // 下载并保存图片
        const fileName = generateFileName(imageType);
        const localPath = await downloadAndSaveImage(imageUrl, fileName, config.outputDir);
        const relativePath = `./${path.relative(process.cwd(), localPath)}`;

        const result: ImageGenerationResult = {
          success: true,
          image: {
            url: relativePath,
            localPath: localPath,
            width: finalSize.width,
            height: finalSize.height,
            format: "png",
            type: imageType,
            prompt: analysis.prompt,
            usageSuggestion: `基于内容分析生成的${imageType}，建议用于网页的相应位置`
          }
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      case "smart_size_inference": {
        const imageType = args.imageType as string;
        const usageContext = args.usageContext as string;

        console.error(`尺寸推断: ${imageType}`);

        const suggestedSize = SIZE_PRESETS[imageType as keyof typeof SIZE_PRESETS];

        if (!suggestedSize) {
          return {
            content: [
              {
                type: "text",
                text: `错误：不支持的图片类型 "${imageType}"。支持的类型：${Object.keys(SIZE_PRESETS).join(", ")}`
              }
            ]
          };
        }

        const result = {
          imageType: imageType,
          suggestedSize: suggestedSize,
          usageContext: usageContext,
          recommendation: `对于${imageType}类型的图片，建议使用${suggestedSize.width}x${suggestedSize.height}尺寸`
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      case "list_available_models": {
        console.error(`列出可用模型`);

        const modelsInfo = AVAILABLE_MODELS.map(model => ({
          id: model.id,
          name: model.name,
          description: model.description,
          strengths: model.strengths,
          suitableFor: model.suitableFor,
          defaultSize: `${model.defaultParams.width}x${model.defaultParams.height}`,
          specialFeatures: model.specialInstructions ? '有特殊指令增强' : '标准模型'
        }));

        const result = {
          totalModels: AVAILABLE_MODELS.length,
          intelligentSelection: "系统会根据图片类型、风格要求、提示词内容自动选择最适合的模型",
          availableModels: modelsInfo,
          selectionCriteria: [
            "图片类型匹配度",
            "风格要求适配性",
            "提示词内容分析",
            "质量要求评估"
          ]
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      default:
        return {
          content: [
            {
              type: "text",
              text: `错误：未知的工具名称 "${name}"`
            }
          ]
        };
    }
  } catch (error) {
    console.error(`工具调用失败 (${name}):`, error);
    return {
      content: [
        {
          type: "text",
          text: `错误：${error instanceof Error ? error.message : '未知错误'}`
        }
      ]
    };
  }
});

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("智慧图片生成 MCP 服务器已启动，等待请求...");
  console.error("支持的工具：generate_image, analyze_and_generate, smart_size_inference");
  console.error("请确保已设置 MODELSCOPE_API_KEY 环境变量");
}

main().catch((error) => {
  console.error("服务器启动失败:", error);
  process.exit(1);
});
