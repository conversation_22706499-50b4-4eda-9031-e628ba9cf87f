小红书的：
import requests
import json
from PIL import Image
from io import BytesIO

url = 'https://api-inference.modelscope.cn/v1/images/generations'

payload = {
    'model': 'yiwanji/FLUX_xiao_hong_shu_ji_zhi_zhen_shi_V2',#ModelScope Model-Id,required
    'prompt': 'A golden cat'# required
}
headers = {
    'Authorization': 'Bearer ms-a6e8141a-97c2-4f11-91ad-373e20a342ba',
    'Content-Type': 'application/json'
}

response = requests.post(url, data=json.dumps(payload, ensure_ascii=False).encode('utf-8'), headers=headers)

response_data = response.json()
image = Image.open(BytesIO(requests.get(response_data['images'][0]['url']).content))
image.save('result_image.jpg')

麦橘超然的：
import requests
import json
from PIL import Image
from io import BytesIO

url = 'https://api-inference.modelscope.cn/v1/images/generations'

payload = {
    'model': 'MAILAND/majicflus_v1',#ModelScope Model-Id,required
    'prompt': 'A golden cat'# required
}
headers = {
    'Authorization': 'Bearer ms-a6e8141a-97c2-4f11-91ad-373e20a342ba',
    'Content-Type': 'application/json'
}

response = requests.post(url, data=json.dumps(payload, ensure_ascii=False).encode('utf-8'), headers=headers)

response_data = response.json()
image = Image.open(BytesIO(requests.get(response_data['images'][0]['url']).content))
image.save('result_image.jpg')

flux-artaug的：
import requests
import json
from PIL import Image
from io import BytesIO

url = 'https://api-inference.modelscope.cn/v1/images/generations'

payload = {
    'model': 'DiffSynth-Studio/FLUX.1-Kontext-dev-lora-ArtAug',#ModelScope Model-Id,required
    'prompt': 'A golden cat'# required
}
headers = {
    'Authorization': 'Bearer ms-a6e8141a-97c2-4f11-91ad-373e20a342ba',
    'Content-Type': 'application/json'
}

response = requests.post(url, data=json.dumps(payload, ensure_ascii=False).encode('utf-8'), headers=headers)

response_data = response.json()
image = Image.open(BytesIO(requests.get(response_data['images'][0]['url']).content))
image.save('result_image.jpg')

